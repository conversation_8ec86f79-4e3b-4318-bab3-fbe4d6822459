import * as Cesium from "cesium";
import { ref, type Ref } from "vue";

// 视角类型定义
export type ViewType = 'nose' | 'tail' | 'free' | 'top';

export interface ViewMode {
  type: ViewType;
  label: string;
  icon: string;
}

export interface ViewInfo {
  title: string;
  tips: string[];
}

export const useAircraftCamera = (
  viewer: Ref<Cesium.Viewer | null>,
  aircraftEntity: Ref<Cesium.Entity | null>
) => {
  const currentView = ref<ViewType>('tail');
  let cameraUpdateListener: Cesium.Event.RemoveCallback | null = null;

  // 视角模式配置
  const viewModes: ViewMode[] = [
    { type: 'nose', label: '机头', icon: '✈️' },
    { type: 'tail', label: '机尾', icon: '🔙' },
    { type: 'free', label: '自由', icon: '🎮' },
    { type: 'top', label: '俯视', icon: '⬇️' }
  ];

  // 视角信息配置
  const viewInfoMap: Record<ViewType, ViewInfo> = {
    nose: {
      title: '机头视角',
      tips: ['相机位于机头前方', '朝向飞机前进方向', '跟随飞机航向', '可拖拽调整角度']
    },
    tail: {
      title: '机尾视角',
      tips: ['相机位于机尾后方', '视角平行于地面', '跟随飞机航向', '可拖拽旋转视角']
    },
    free: {
      title: '自由视角',
      tips: ['相机完全自由移动', '不跟随飞机', '可任意角度观察', '鼠标控制移动旋转']
    },
    top: {
      title: '俯视视角',
      tips: ['相机位于飞机正上方', '垂直俯视观察', '跟随飞机移动', '固定俯视角度']
    }
  };

  // 获取当前视角信息
  const getCurrentViewInfo = (): ViewInfo => {
    return viewInfoMap[currentView.value];
  };

  // 机头视角相机更新函数
  const updateNoseCamera = () => {
    if (!aircraftEntity.value || !viewer.value) return;

    const position = aircraftEntity.value.position?.getValue(viewer.value.clock.currentTime);
    const orientation = aircraftEntity.value.orientation?.getValue(viewer.value.clock.currentTime);

    if (position && orientation) {
      const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);
      
      // 相机位置在飞机前方
      const localOffset = new Cesium.Cartesian3(0, 20, 5); // 前方20米，上方5米
      
      const transform = Cesium.Transforms.headingPitchRollToFixedFrame(position, hpr);
      const worldOffset = Cesium.Matrix4.multiplyByPoint(transform, localOffset, new Cesium.Cartesian3());

      viewer.value.scene.camera.setView({
        destination: worldOffset,
        orientation: {
          heading: hpr.heading,
          pitch: Cesium.Math.toRadians(-10), // 略微向下看
          roll: 0,
        },
      });
    }
  };

  // 机尾视角相机更新函数
  const updateTailCamera = () => {
    if (!aircraftEntity.value || !viewer.value) return;

    const position = aircraftEntity.value.position?.getValue(viewer.value.clock.currentTime);
    const orientation = aircraftEntity.value.orientation?.getValue(viewer.value.clock.currentTime);

    if (position && orientation) {
      const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);
      
      // 相机位置在飞机后方（尾翼位置）
      const localOffset = new Cesium.Cartesian3(0, -15, 3); // 后方15米，上方3米
      
      const transform = Cesium.Transforms.headingPitchRollToFixedFrame(position, hpr);
      const worldOffset = Cesium.Matrix4.multiplyByPoint(transform, localOffset, new Cesium.Cartesian3());

      viewer.value.scene.camera.setView({
        destination: worldOffset,
        orientation: {
          heading: hpr.heading,
          pitch: 0, // 平行于地面
          roll: 0,
        },
      });
    }
  };

  // 俯视视角相机更新函数
  const updateTopCamera = () => {
    if (!aircraftEntity.value || !viewer.value) return;

    const position = aircraftEntity.value.position?.getValue(viewer.value.clock.currentTime);
    const orientation = aircraftEntity.value.orientation?.getValue(viewer.value.clock.currentTime);

    if (position && orientation) {
      const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);
      
      // 相机位置在飞机正上方
      const localOffset = new Cesium.Cartesian3(0, 0, 100); // 正上方100米
      
      const transform = Cesium.Transforms.headingPitchRollToFixedFrame(position, hpr);
      const worldOffset = Cesium.Matrix4.multiplyByPoint(transform, localOffset, new Cesium.Cartesian3());

      viewer.value.scene.camera.setView({
        destination: worldOffset,
        orientation: {
          heading: hpr.heading,
          pitch: Cesium.Math.toRadians(-90), // 垂直向下
          roll: 0,
        },
      });
    }
  };

  // 启用相机控制器
  const enableCameraControls = () => {
    if (!viewer.value) return;
    
    viewer.value.scene.screenSpaceCameraController.enableRotate = true;
    viewer.value.scene.screenSpaceCameraController.enableZoom = true;
    viewer.value.scene.screenSpaceCameraController.enableTilt = true;
    viewer.value.scene.screenSpaceCameraController.enableLook = true;
  };

  // 切换视角模式
  const switchCameraView = (viewType: ViewType) => {
    if (!viewer.value || !aircraftEntity.value) return;

    currentView.value = viewType;

    // 移除之前的监听器
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }

    switch (viewType) {
      case 'nose':
        viewer.value.trackedEntity = undefined;
        enableCameraControls();
        updateNoseCamera();
        cameraUpdateListener = viewer.value.clock.onTick.addEventListener(updateNoseCamera);
        break;

      case 'tail':
        viewer.value.trackedEntity = undefined;
        enableCameraControls();
        updateTailCamera();
        cameraUpdateListener = viewer.value.clock.onTick.addEventListener(updateTailCamera);
        break;

      case 'top':
        viewer.value.trackedEntity = undefined;
        enableCameraControls();
        updateTopCamera();
        cameraUpdateListener = viewer.value.clock.onTick.addEventListener(updateTopCamera);
        break;

      case 'free':
        viewer.value.trackedEntity = undefined;
        enableCameraControls();
        // 自由视角不需要特殊的相机更新逻辑
        break;

      default:
        // 默认使用机尾视角
        switchCameraView('tail');
        break;
    }
  };

  // 清理函数
  const cleanup = () => {
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }
  };

  return {
    currentView,
    viewModes,
    getCurrentViewInfo,
    switchCameraView,
    cleanup
  };
};
