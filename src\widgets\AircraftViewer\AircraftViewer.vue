<template>
  <div class="mainContainer">
    <div id="cesium-viewer" ref="viewerContainer"></div>
    <!-- 视角切换控制面板 -->
    <div class="camera-controls">
      <button
        @click="toggleCameraMode"
        :class="['camera-btn', { active: isFirstPersonView }]"
      >
        {{ isFirstPersonView ? "第一视角" : "第三视角" }}
      </button>
      <button @click="resetAnimation" class="reset-btn">重置动画</button>
      <div v-if="isFirstPersonView" class="first-person-tips">
        <small>第一视角模式：</small>
        <small>• 尾翼视角观察</small>
        <small>• 视角平行于地面</small>
        <small>• 跟随飞机航向</small>
        <small>• 鼠标拖拽旋转视角</small>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from "cesium";

import { onBeforeUnmount, onMounted, ref } from "vue";

import { createModel } from "@/utils/createModel";
import { initMap } from "@/utils/initMap";

const viewerContainer = ref<HTMLElement | null>(null);
let viewer: Cesium.Viewer | null = null;
const planURL = "/src/assets/models/plan1.glb";

let aircraftEntity: Cesium.Entity | null = null;
let animationInterval: number | null = null;

// 视角控制相关变量
const isFirstPersonView = ref(false);
let cameraUpdateListener: Cesium.Event.RemoveCallback | null = null;
// 在北京天安门上空创建飞机模型，设置朝向
// createModel(
//   viewer,
//   planURL,
//   2000, // 高度2000米
//   116.4074, // 北京经度
//   39.9042, // 北京纬度
//   225, // 朝向角度，使飞机朝向东南方
//   10, // 50, // 机头略微向上
//   0, // 0, // 不倾斜
// );

const initViewer = async () => {
  viewer = await initMap(viewerContainer);
  viewer.entities.removeAll();

  const startLon = 116.3975; // 天安门
  const startLat = 39.9087;
  const endLon = 116.3269; // 清华大学
  const endLat = 40.0032;

  // 定义飞行关键高度点：起飞 -> 上升 -> 巡航 -> 下降
  const heights = [100, 800, 1500, 2000, 2000, 2000, 2000, 2000];
  const pointCount = heights.length;
  const secondsBetweenPoints = 3;

  const start = Cesium.JulianDate.now();
  const position = new Cesium.SampledPositionProperty();

  // 设置插值算法为 Hermite，多点之间平滑曲线
  position.setInterpolationOptions({
    interpolationDegree: 5,
    interpolationAlgorithm: Cesium.HermitePolynomialApproximation,
  });

  for (let i = 0; i < pointCount; i++) {
    const lon = startLon + ((endLon - startLon) * i) / (pointCount - 1);
    const lat = startLat + ((endLat - startLat) * i) / (pointCount - 1);
    const height = heights[i];

    const time = Cesium.JulianDate.addSeconds(
      start,
      i * secondsBetweenPoints,
      new Cesium.JulianDate(),
    );
    const positionSample = Cesium.Cartesian3.fromDegrees(lon, lat, height);
    position.addSample(time, positionSample);
  }

  const stop = Cesium.JulianDate.addSeconds(
    start,
    (pointCount - 1) * secondsBetweenPoints,
    new Cesium.JulianDate(),
  );

  aircraftEntity = viewer.entities.add({
    availability: new Cesium.TimeIntervalCollection([
      new Cesium.TimeInterval({ start, stop }),
    ]),
    position,
    orientation: new Cesium.VelocityOrientationProperty(position),
    model: {
      uri: planURL,
      minimumPixelSize: 64,
      maximumScale: 200,
      scale: 2,
    },
  });

  viewer.clock.startTime = start.clone();
  viewer.clock.stopTime = stop.clone();
  viewer.clock.currentTime = start.clone();
  viewer.clock.clockRange = Cesium.ClockRange.CLAMPED;
  viewer.clock.multiplier = 1;
  viewer.clock.shouldAnimate = true;

  // 默认使用第三视角（跟踪模式）
  viewer.trackedEntity = aircraftEntity;

  // 设置初始相机视角
  // viewer.camera.setView({
  //   destination: Cesium.Cartesian3.fromDegrees(116.45, 39.9042, 5000),
  //   orientation: {
  //     heading: Cesium.Math.toRadians(90),
  //     pitch: Cesium.Math.toRadians(-45),
  //     roll: 0.0,
  //   },
  // });
};

// 第一视角相机更新函数
const updateFirstPersonCamera = () => {
  if (!aircraftEntity || !viewer) return;

  const position = aircraftEntity.position?.getValue(viewer.clock.currentTime);
  const orientation = aircraftEntity.orientation?.getValue(
    viewer.clock.currentTime,
  );

  if (position && orientation) {
    // 获取飞机的朝向角度
    const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);

    // 计算相机在飞机坐标系中的偏移位置（尾翼位置）
    const localOffset = new Cesium.Cartesian3(0, -15, 3); // 后方15米，上方3米

    // 将本地偏移转换为世界坐标
    const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
      position,
      hpr,
    );
    const worldOffset = Cesium.Matrix4.multiplyByPoint(
      transform,
      localOffset,
      new Cesium.Cartesian3(),
    );

    // 设置相机朝向：与飞机朝向一致，但俯仰角设为0（平行于地面）
    const cameraHeading = hpr.heading; // 保持飞机的航向
    const cameraPitch = 0; // 平行于地面
    const cameraRoll = 0; // 不倾斜

    // 设置相机位置和朝向
    viewer.scene.camera.setView({
      destination: worldOffset,
      orientation: {
        heading: cameraHeading,
        pitch: cameraPitch,
        roll: cameraRoll,
      },
    });
  }
};

// 切换视角模式
const toggleCameraMode = () => {
  if (!viewer || !aircraftEntity) return;

  isFirstPersonView.value = !isFirstPersonView.value;

  if (isFirstPersonView.value) {
    // 切换到第一视角
    viewer.trackedEntity = undefined;

    // 启用相机控制器，允许用户交互
    viewer.scene.screenSpaceCameraController.enableRotate = true;
    viewer.scene.screenSpaceCameraController.enableZoom = true;
    viewer.scene.screenSpaceCameraController.enableTilt = true;
    viewer.scene.screenSpaceCameraController.enableLook = true;

    // 移除之前的监听器
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }

    // 立即设置第一视角位置
    updateFirstPersonCamera();

    // 添加第一视角相机更新监听器（仅更新位置，不锁定朝向）
    cameraUpdateListener = viewer.clock.onTick.addEventListener(() => {
      if (!aircraftEntity || !viewer) return;

      const position = aircraftEntity.position?.getValue(
        viewer.clock.currentTime,
      );
      const orientation = aircraftEntity.orientation?.getValue(
        viewer.clock.currentTime,
      );

      if (position && orientation) {
        // 获取飞机的朝向角度
        const hpr = Cesium.HeadingPitchRoll.fromQuaternion(orientation);

        // 计算相机在飞机坐标系中的偏移位置（尾翼位置）
        const localOffset = new Cesium.Cartesian3(0, -15, 3); // 后方15米，上方3米

        // 将本地偏移转换为世界坐标
        const transform = Cesium.Transforms.headingPitchRollToFixedFrame(
          position,
          hpr,
        );
        const worldOffset = Cesium.Matrix4.multiplyByPoint(
          transform,
          localOffset,
          new Cesium.Cartesian3(),
        );

        // 更新相机位置，并设置朝向为平行于地面
        viewer.scene.camera.position = worldOffset;

        // 设置相机朝向：与飞机朝向一致，但平行于地面
        viewer.scene.camera.setView({
          destination: worldOffset,
          orientation: {
            heading: hpr.heading, // 保持飞机的航向
            pitch: 0, // 平行于地面
            roll: 0, // 不倾斜
          },
        });
      }
    });
  } else {
    // 切换到第三视角
    if (cameraUpdateListener) {
      cameraUpdateListener();
      cameraUpdateListener = null;
    }

    // 恢复跟踪模式
    viewer.trackedEntity = aircraftEntity;

    // 恢复默认相机控制设置
    viewer.scene.screenSpaceCameraController.enableRotate = true;
    viewer.scene.screenSpaceCameraController.enableZoom = true;
    viewer.scene.screenSpaceCameraController.enableTilt = true;
    viewer.scene.screenSpaceCameraController.enableLook = true;
  }
};

// 重置动画
const resetAnimation = () => {
  if (!viewer) return;

  viewer.clock.currentTime = viewer.clock.startTime.clone();
  viewer.clock.shouldAnimate = true;

  // 如果是第一视角，重新设置相机
  if (isFirstPersonView.value) {
    updateFirstPersonCamera();
  }
};

onMounted(() => {
  initViewer();
});

onBeforeUnmount(() => {
  if (cameraUpdateListener) {
    cameraUpdateListener();
  }
  if (viewer) {
    viewer.destroy();
  }
  if (animationInterval) {
    clearInterval(animationInterval);
  }
});
</script>

<style scoped>
.mainContainer {
  position: relative;
  width: 100%;
  height: 100vh;
}

#cesium-viewer {
  width: 100%;
  height: 100%;
}

.camera-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
}

.camera-btn,
.reset-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  background: rgba(42, 42, 42, 0.8);
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.camera-btn:hover,
.reset-btn:hover {
  background: rgba(42, 42, 42, 0.9);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.camera-btn.active {
  background: rgba(0, 122, 255, 0.8);
  border-color: rgba(0, 122, 255, 0.3);
}

.camera-btn.active:hover {
  background: rgba(0, 122, 255, 0.9);
}

.first-person-tips {
  background: rgba(42, 42, 42, 0.9);
  border-radius: 6px;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.first-person-tips small {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  line-height: 1.4;
  margin: 2px 0;
}

.first-person-tips small:first-child {
  color: rgba(0, 122, 255, 0.9);
  font-weight: 500;
  margin-bottom: 4px;
}
</style>
